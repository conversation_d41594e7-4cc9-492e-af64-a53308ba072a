/* Sellzio Checkout Styles */

/* CSS Reset untuk Checkout Page - <PERSON><PERSON><PERSON> */
.sellzio-checkout-page {
  /* Reset semua styling yang mungkin dioverride oleh global CSS */
  all: initial !important;
  font-family: '<PERSON><PERSON>', Aria<PERSON>, sans-serif !important;
}

/* Reset untuk semua elemen di dalam checkout page */
.sellzio-checkout-page *,
.sellzio-checkout-page *::before,
.sellzio-checkout-page *::after {
  all: unset !important;
  display: revert !important;
  box-sizing: border-box !important;
  font-family: '<PERSON><PERSON>', Arial, sans-serif !important;
}

/* Reset khusus untuk elemen yang sering bermasalah */
.sellzio-checkout-page div,
.sellzio-checkout-page span,
.sellzio-checkout-page p,
.sellzio-checkout-page h1,
.sellzio-checkout-page h2,
.sellzio-checkout-page h3,
.sellzio-checkout-page button,
.sellzio-checkout-page input,
.sellzio-checkout-page label {
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  background: transparent !important;
  color: inherit !important;
  font-family: '<PERSON><PERSON>', Aria<PERSON>, sans-serif !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
  text-decoration: none !important;
  list-style: none !important;
  vertical-align: baseline !important;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Page Layout - Maximum Specificity */
.sellzio-checkout-page.sellzio-checkout-page.sellzio-checkout-page {
  min-height: 100vh !important;
  background-color: #f5f5f5 !important;
  color: #333 !important;
  line-height: 1.6 !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  width: 100% !important;
  overflow-x: hidden !important;
  position: relative !important;
  display: flex !important;
  flex-direction: column !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  text-decoration: none !important;
  list-style: none !important;
  vertical-align: baseline !important;
}

/* Ensure proper box-sizing for checkout page */
.sellzio-checkout-page *,
.sellzio-checkout-page *::before,
.sellzio-checkout-page *::after {
  box-sizing: border-box !important;
  font-family: 'Roboto', Arial, sans-serif !important;
}

/* Header - Maximum Specificity */
.sellzio-checkout-page .sellzio-checkout-header.sellzio-checkout-header {
  position: sticky !important;
  top: 0 !important;
  width: 100% !important;
  background-color: #fff !important;
  padding: 15px 0 !important;
  text-align: center !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  z-index: 100 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
  border: none !important;
  outline: none !important;
  font-family: 'Roboto', Arial, sans-serif !important;
}

@media (min-width: 800px) {
  .sellzio-checkout-page .sellzio-checkout-header-container.sellzio-checkout-header-container {
    max-width: 800px !important;
    margin: 0 auto !important;
    position: relative !important;
    width: 100% !important;
  }
}

.sellzio-checkout-page .sellzio-checkout-header-container.sellzio-checkout-header-container {
  max-width: 800px !important;
  margin: 0 auto !important;
  position: relative !important;
  width: 100% !important;
  padding: 0 15px !important;
  font-family: 'Roboto', Arial, sans-serif !important;
}

.sellzio-checkout-page .sellzio-checkout-title.sellzio-checkout-title {
  font-size: 1.2rem !important;
  font-weight: bold !important;
  margin: 0 !important;
  color: #333 !important;
  font-family: 'Roboto', Arial, sans-serif !important;
}

.sellzio-checkout-page .sellzio-checkout-back-btn.sellzio-checkout-back-btn {
  position: absolute !important;
  left: 15px !important;
  border: none !important;
  background: none !important;
  font-size: 1.2rem !important;
  cursor: pointer !important;
  padding: 5px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #333 !important;
  font-family: 'Roboto', Arial, sans-serif !important;
}

/* Container - Maximum Specificity */
.sellzio-checkout-page .sellzio-checkout-container.sellzio-checkout-container {
  max-width: 800px !important;
  margin: 3px auto 0 !important;
  padding: 15px !important;
  padding-bottom: 120px !important; /* Space for fixed footer */
  min-height: calc(100vh - 80px) !important; /* Ensure full height minus header */
  width: 100% !important;
  flex: 1 !important;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  font-family: 'Roboto', Arial, sans-serif !important;
}

/* Section - Maximum Specificity */
.sellzio-checkout-page .sellzio-checkout-section.sellzio-checkout-section {
  background-color: #fff !important;
  border-radius: 8px !important;
  padding: 15px !important;
  margin-bottom: 8px !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
  border: none !important;
  outline: none !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  color: #333 !important;
}

.sellzio-checkout-page .sellzio-section-title.sellzio-section-title {
  font-size: 1rem !important;
  font-weight: bold !important;
  margin-bottom: 10px !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  color: #333 !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-edit-btn.sellzio-edit-btn {
  color: #ee4d2d !important;
  font-size: 0.9rem !important;
  font-weight: normal !important;
  background: none !important;
  border: none !important;
  cursor: pointer !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  padding: 0 !important;
  margin: 0 !important;
  outline: none !important;
}

/* Address Section - Maximum Specificity */
.sellzio-checkout-page .sellzio-address-info.sellzio-address-info {
  margin-bottom: 6px !important;
  font-size: 14px !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  color: #333 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-address-name-phone.sellzio-address-name-phone {
  margin-bottom: 4px !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-address-name.sellzio-address-name {
  font-weight: bold !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  color: #333 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-address-phone.sellzio-address-phone {
  margin-left: 10px !important;
  color: #666 !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-address-text.sellzio-address-text {
  color: #666 !important;
  margin-top: 4px !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

/* Product Section - Maximum Specificity */
.sellzio-checkout-page .sellzio-product-container.sellzio-product-container {
  margin-bottom: 10px !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-store-group.sellzio-store-group {
  margin-bottom: 15px !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-store-group.sellzio-store-group:last-child {
  margin-bottom: 0 !important;
}

.sellzio-checkout-page .sellzio-product-item.sellzio-product-item {
  display: flex !important;
  padding: 10px 0 !important;
  border-bottom: 1px solid #f0f0f0 !important;
  flex-direction: column !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  margin: 0 !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-product-item.sellzio-product-item:last-child {
  border-bottom: none !important;
}

.sellzio-checkout-page .sellzio-product-shop.sellzio-product-shop {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 8px !important;
  font-size: 14px !important;
  color: #333 !important;
  font-weight: 500 !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-shop-icon.sellzio-shop-icon {
  margin-right: 8px !important;
  display: flex !important;
  align-items: center !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-product-content.sellzio-product-content {
  display: flex !important;
  margin-top: 5px !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-product-image.sellzio-product-image {
  width: 80px !important;
  height: 80px !important;
  border-radius: 4px !important;
  object-fit: cover !important;
  margin-right: 15px !important;
  border: none !important;
  outline: none !important;
}

.sellzio-checkout-page .sellzio-product-details.sellzio-product-details {
  flex: 1 !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-product-name.sellzio-product-name {
  font-weight: bold !important;
  font-size: 16px !important;
  margin-bottom: 5px !important;
  color: #333 !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-product-variant.sellzio-product-variant {
  font-size: 0.8rem !important;
  color: #666 !important;
  margin-bottom: 5px !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-product-actions.sellzio-product-actions {
  display: flex !important;
  justify-content: flex-start !important;
  align-items: center !important;
  margin-top: 10px !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-quantity-display.sellzio-quantity-display {
  font-size: 0.9rem !important;
  color: #666 !important;
  margin-left: auto !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-quantity.sellzio-quantity {
  font-weight: bold !important;
  color: #333 !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-product-price.sellzio-product-price {
  color: #ee4d2d !important;
  font-weight: bold !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-price-original.sellzio-price-original {
  color: #999 !important;
  text-decoration: line-through !important;
  font-weight: normal !important;
  font-size: 0.85em !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

/* Shipping Section */
.sellzio-shipping-option-selected {
  position: relative;
  border: 1px solid #00BFA5;
  border-radius: 8px;
  padding: 15px;
  margin-top: 10px;
  cursor: pointer;
  background-color: #fff;
}

.sellzio-free-shipping-badge {
  position: absolute;
  top: -10px;
  left: 15px;
  background-color: #00BFA5;
  color: white;
  padding: 2px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  z-index: 1;
}

.sellzio-shipping-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  align-items: center;
}

.sellzio-shipping-name {
  font-weight: bold;
  font-size: 14px;
}

.sellzio-shipping-price-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sellzio-shipping-original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 14px;
  margin-right: 8px;
}

.sellzio-shipping-price {
  font-weight: bold;
  font-size: 16px;
}

.sellzio-shipping-check {
  color: #00BFA5;
  margin-left: 5px;
}

.sellzio-shipping-estimate {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  color: #00BFA5;
}

.sellzio-truck-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.sellzio-estimate-text {
  font-weight: 500;
  color: #00BFA5;
  font-size: 12px;
}

.sellzio-shipping-guarantee {
  color: #666;
  font-size: 12px;
  margin-bottom: -10px;
}

/* Payment Section */
.sellzio-payment-section {
  cursor: pointer;
}

.sellzio-payment-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sellzio-payment-left {
  display: flex;
  align-items: center;
}

.sellzio-payment-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.sellzio-payment-label {
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
}

.sellzio-payment-right {
  display: flex;
  align-items: center;
}

.sellzio-payment-method {
  margin-right: 10px;
  color: #666;
  font-size: 14px;
}

.sellzio-payment-arrow {
  color: #999;
  font-size: 30px;
}

/* Voucher Section */
.sellzio-voucher-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  cursor: pointer;
}

.sellzio-voucher-label {
  display: flex;
  align-items: center;
}

.sellzio-voucher-icon {
  margin-right: 10px;
  font-size: 1.2rem;
}

.sellzio-voucher-applied {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sellzio-voucher-count {
  color: #00BFA5;
  font-size: 14px;
}

/* Payment Summary */
.sellzio-cost-breakdown {
  margin-top: 10px;
}

.sellzio-cost-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.sellzio-discount {
  color: #00BFA5;
}

.sellzio-total-cost {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  font-size: 1.1rem;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.sellzio-total-price {
  color: #ee4d2d;
}

/* Footer - Maximum Specificity */
.sellzio-checkout-page .sellzio-checkout-footer.sellzio-checkout-footer {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  background-color: #fff !important;
  box-shadow: 0 -2px 4px rgba(0,0,0,0.1) !important;
  z-index: 50 !important;
  border-radius: 8px !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  font-family: 'Roboto', Arial, sans-serif !important;
}

.sellzio-checkout-page .sellzio-checkout-footer-content.sellzio-checkout-footer-content {
  max-width: 800px !important;
  margin: 0 auto !important;
  padding: 10px 15px !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  background: transparent !important;
  border: none !important;
  outline: none !important;
}

.sellzio-checkout-page .sellzio-order-total.sellzio-order-total {
  font-size: 1.1rem !important;
  font-weight: bold !important;
  color: #ee4d2d !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;
}

.sellzio-checkout-page .sellzio-order-btn.sellzio-order-btn {
  background-color: #ee4d2d !important;
  color: white !important;
  border: none !important;
  padding: 12px 25px !important;
  border-radius: 4px !important;
  font-weight: bold !important;
  cursor: pointer !important;
  transition: background-color 0.3s !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  margin: 0 !important;
  outline: none !important;
}

.sellzio-checkout-page .sellzio-order-btn.sellzio-order-btn:hover {
  background-color: #d63c1e !important;
}

.sellzio-checkout-page .sellzio-order-btn.sellzio-order-btn:disabled {
  background-color: #aaa !important;
  cursor: not-allowed !important;
}

/* Modal Styles */
.sellzio-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.sellzio-modal {
  background-color: #fff;
  border-radius: 8px;
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.sellzio-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.sellzio-modal-header h2 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: bold;
}

.sellzio-modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  color: #666;
}

.sellzio-modal-content {
  padding: 20px;
}

.sellzio-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #e0e0e0;
}

/* Form Styles */
.sellzio-form-group {
  margin-bottom: 20px;
}

.sellzio-form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.sellzio-form-input,
.sellzio-form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.sellzio-form-input:focus,
.sellzio-form-textarea:focus {
  outline: none;
  border-color: #ee4d2d;
}

.sellzio-form-input.error,
.sellzio-form-textarea.error {
  border-color: #ef4444;
}

.sellzio-form-error {
  display: block;
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

.sellzio-form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* Button Styles */
.sellzio-btn-primary {
  background-color: #ee4d2d;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

.sellzio-btn-primary:hover {
  background-color: #d63c1e;
}

.sellzio-btn-secondary {
  background-color: #fff;
  color: #666;
  border: 1px solid #ddd;
  padding: 12px 24px;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s;
}

.sellzio-btn-secondary:hover {
  background-color: #f5f5f5;
}

/* Toast Notification */
.sellzio-toast {
  position: fixed;
  top: 70px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0,0,0,0.7);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  z-index: 1000;
  font-size: 14px;
  max-width: 300px;
  text-align: center;
}

.sellzio-toast-success {
  background-color: rgba(34, 197, 94, 0.9);
}

.sellzio-toast-error {
  background-color: rgba(239, 68, 68, 0.9);
}

.sellzio-toast-info {
  background-color: rgba(0,0,0,0.7);
}

/* Shipping Options Modal */
.sellzio-shipping-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sellzio-shipping-option-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.sellzio-shipping-option-item:hover {
  border-color: #ee4d2d;
}

.sellzio-shipping-option-item.selected {
  border-color: #ee4d2d;
  background-color: #fef7f0;
}

.sellzio-shipping-option-radio {
  margin-right: 15px;
}

.sellzio-shipping-option-radio input[type="radio"] {
  width: 18px;
  height: 18px;
  accent-color: #ee4d2d;
}

.sellzio-shipping-option-details {
  flex: 1;
}

.sellzio-shipping-option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.sellzio-shipping-option-name {
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sellzio-free-badge {
  background-color: #00BFA5;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
}

.sellzio-shipping-option-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sellzio-original-price {
  color: #999;
  text-decoration: line-through;
  font-size: 12px;
}

.sellzio-current-price {
  font-weight: bold;
  color: #ee4d2d;
}

.sellzio-shipping-option-estimate {
  color: #666;
  font-size: 12px;
}

/* Payment Methods Modal */
.sellzio-payment-groups {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sellzio-payment-group-title {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.sellzio-payment-method-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 8px;
}

.sellzio-payment-method-item:hover {
  border-color: #ee4d2d;
}

.sellzio-payment-method-item.selected {
  border-color: #ee4d2d;
  background-color: #fef7f0;
}

.sellzio-payment-method-radio {
  margin-right: 12px;
}

.sellzio-payment-method-radio input[type="radio"] {
  width: 18px;
  height: 18px;
  accent-color: #ee4d2d;
}

.sellzio-payment-method-icon {
  margin-right: 12px;
  font-size: 20px;
}

.sellzio-payment-method-name {
  font-weight: 500;
}

/* Responsive */
@media (max-width: 480px) {
  .sellzio-checkout-container {
    padding: 10px;
  }

  .sellzio-checkout-section {
    padding: 12px;
  }

  .sellzio-product-image {
    width: 60px;
    height: 60px;
  }

  .sellzio-checkout-footer-content {
    padding: 10px;
  }

  .sellzio-order-btn {
    padding: 10px 20px;
  }

  .sellzio-toast {
    max-width: 250px;
    font-size: 13px;
  }

  .sellzio-modal {
    margin: 10px;
    max-height: 95vh;
  }

  .sellzio-modal-header,
  .sellzio-modal-content,
  .sellzio-modal-footer {
    padding: 15px;
  }

  .sellzio-shipping-option-item,
  .sellzio-payment-method-item {
    padding: 10px;
  }

  .sellzio-modal-footer {
    flex-direction: column;
  }

  .sellzio-btn-primary,
  .sellzio-btn-secondary {
    width: 100%;
    margin-bottom: 10px;
  }

  .sellzio-btn-primary {
    order: 1;
  }

  .sellzio-btn-secondary {
    order: 2;
    margin-bottom: 0;
  }
}
