/* CSS Isolation untuk Sellzio Checkout - Mengatasi konflik dengan global CSS */

/* Reset global yang sangat kuat untuk checkout page */
.sellzio-checkout-page,
.sellzio-checkout-page * {
  /* Reset semua property yang mungkin dioverride */
  all: unset !important;
  display: revert !important;
  box-sizing: border-box !important;
}

/* Reapply essential styles untuk checkout page */
.sellzio-checkout-page {
  min-height: 100vh !important;
  background-color: #f5f5f5 !important;
  color: #333 !important;
  line-height: 1.6 !important;
  font-family: 'Roboto', Arial, sans-serif !important;
  width: 100% !important;
  overflow-x: hidden !important;
  position: relative !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Force font family untuk semua text elements */
.sellzio-checkout-page,
.sellzio-checkout-page *,
.sellzio-checkout-page h1,
.sellzio-checkout-page h2,
.sellzio-checkout-page h3,
.sellzio-checkout-page p,
.sellzio-checkout-page span,
.sellzio-checkout-page div,
.sellzio-checkout-page button,
.sellzio-checkout-page input,
.sellzio-checkout-page label {
  font-family: '<PERSON><PERSON>', Aria<PERSON>, sans-serif !important;
  color: inherit !important;
}

/* Pastikan tidak ada margin/padding yang tidak diinginkan */
.sellzio-checkout-page * {
  margin: 0 !important;
  padding: 0 !important;
}

/* Reapply margin/padding hanya untuk elemen yang membutuhkan */
.sellzio-checkout-page .sellzio-checkout-container {
  padding: 15px !important;
  padding-bottom: 120px !important;
}

.sellzio-checkout-page .sellzio-checkout-section {
  padding: 15px !important;
  margin-bottom: 8px !important;
}

/* Force background colors */
.sellzio-checkout-page {
  background-color: #f5f5f5 !important;
}

.sellzio-checkout-page .sellzio-checkout-section,
.sellzio-checkout-page .sellzio-checkout-header,
.sellzio-checkout-page .sellzio-checkout-footer {
  background-color: #fff !important;
}

/* Force text colors */
.sellzio-checkout-page {
  color: #333 !important;
}

.sellzio-checkout-page .sellzio-address-phone,
.sellzio-checkout-page .sellzio-address-text,
.sellzio-checkout-page .sellzio-product-variant,
.sellzio-checkout-page .sellzio-quantity-display {
  color: #666 !important;
}

.sellzio-checkout-page .sellzio-edit-btn {
  color: #ee4d2d !important;
}

.sellzio-checkout-page .sellzio-product-price,
.sellzio-checkout-page .sellzio-order-total {
  color: #ee4d2d !important;
}

.sellzio-checkout-page .sellzio-price-original {
  color: #999 !important;
}

/* Force button styles */
.sellzio-checkout-page .sellzio-order-btn {
  background-color: #ee4d2d !important;
  color: white !important;
  border: none !important;
  padding: 12px 25px !important;
  border-radius: 4px !important;
  font-weight: bold !important;
  cursor: pointer !important;
}

.sellzio-checkout-page .sellzio-order-btn:hover {
  background-color: #d63c1e !important;
}

.sellzio-checkout-page .sellzio-order-btn:disabled {
  background-color: #aaa !important;
  cursor: not-allowed !important;
}

/* Force layout styles */
.sellzio-checkout-page .sellzio-checkout-header {
  position: sticky !important;
  top: 0 !important;
  width: 100% !important;
  padding: 15px 0 !important;
  text-align: center !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  z-index: 100 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.sellzio-checkout-page .sellzio-checkout-footer {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  box-shadow: 0 -2px 4px rgba(0,0,0,0.1) !important;
  z-index: 50 !important;
}

.sellzio-checkout-page .sellzio-checkout-footer-content {
  max-width: 800px !important;
  margin: 0 auto !important;
  padding: 10px 15px !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

.sellzio-checkout-page .sellzio-checkout-container {
  max-width: 800px !important;
  margin: 3px auto 0 !important;
  width: 100% !important;
  flex: 1 !important;
}

/* Force flex layouts */
.sellzio-checkout-page .sellzio-section-title {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 10px !important;
}

.sellzio-checkout-page .sellzio-product-item {
  display: flex !important;
  flex-direction: column !important;
  padding: 10px 0 !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.sellzio-checkout-page .sellzio-product-content {
  display: flex !important;
  margin-top: 5px !important;
}

.sellzio-checkout-page .sellzio-product-details {
  flex: 1 !important;
}

.sellzio-checkout-page .sellzio-product-actions {
  display: flex !important;
  justify-content: flex-start !important;
  align-items: center !important;
  margin-top: 10px !important;
}

.sellzio-checkout-page .sellzio-product-price {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

/* Force font weights */
.sellzio-checkout-page .sellzio-checkout-title,
.sellzio-checkout-page .sellzio-section-title,
.sellzio-checkout-page .sellzio-address-name,
.sellzio-checkout-page .sellzio-product-name,
.sellzio-checkout-page .sellzio-quantity,
.sellzio-checkout-page .sellzio-product-price,
.sellzio-checkout-page .sellzio-order-total,
.sellzio-checkout-page .sellzio-order-btn {
  font-weight: bold !important;
}

/* Force font sizes */
.sellzio-checkout-page .sellzio-checkout-title {
  font-size: 1.2rem !important;
}

.sellzio-checkout-page .sellzio-section-title {
  font-size: 1rem !important;
}

.sellzio-checkout-page .sellzio-address-info {
  font-size: 14px !important;
}

.sellzio-checkout-page .sellzio-product-name {
  font-size: 16px !important;
}

.sellzio-checkout-page .sellzio-product-variant {
  font-size: 0.8rem !important;
}

.sellzio-checkout-page .sellzio-quantity-display {
  font-size: 0.9rem !important;
}

.sellzio-checkout-page .sellzio-order-total {
  font-size: 1.1rem !important;
}

.sellzio-checkout-page .sellzio-edit-btn {
  font-size: 0.9rem !important;
  font-weight: normal !important;
}

/* Force borders dan shadows */
.sellzio-checkout-page .sellzio-checkout-section {
  border-radius: 8px !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

.sellzio-checkout-page .sellzio-product-image {
  width: 80px !important;
  height: 80px !important;
  border-radius: 4px !important;
  object-fit: cover !important;
  margin-right: 15px !important;
}

/* Force cursor styles */
.sellzio-checkout-page .sellzio-edit-btn,
.sellzio-checkout-page .sellzio-order-btn,
.sellzio-checkout-page .sellzio-checkout-back-btn {
  cursor: pointer !important;
}

/* Force positioning */
.sellzio-checkout-page .sellzio-checkout-back-btn {
  position: absolute !important;
  left: 15px !important;
}

.sellzio-checkout-page .sellzio-quantity-display {
  margin-left: auto !important;
}

/* Force text decorations */
.sellzio-checkout-page .sellzio-price-original {
  text-decoration: line-through !important;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .sellzio-checkout-page .sellzio-checkout-container {
    padding: 10px !important;
  }
  
  .sellzio-checkout-page .sellzio-checkout-section {
    padding: 12px !important;
  }
  
  .sellzio-checkout-page .sellzio-product-image {
    width: 60px !important;
    height: 60px !important;
  }
  
  .sellzio-checkout-page .sellzio-checkout-footer-content {
    padding: 10px !important;
  }
  
  .sellzio-checkout-page .sellzio-order-btn {
    padding: 10px 20px !important;
  }
}
