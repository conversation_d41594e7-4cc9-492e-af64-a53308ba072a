/* CSS Isolation untuk Sellzio Checkout - Mengembalikan layout sesuai desain asli */

/* Reset hanya untuk elemen yang bermasalah, bukan semua elemen */
.sellzio-checkout-page {
  /* Reset hanya property yang conflict dengan global CSS */
  margin: 0 !important;
  padding: 0 !important;
  box-sizing: border-box !important;
  font-family: '<PERSON><PERSON>', Aria<PERSON>, sans-serif !important;

  /* Layout dasar sesuai desain HTML */
  min-height: 100vh !important;
  background-color: #f5f5f5 !important;
  color: #333 !important;
  line-height: 1.6 !important;
  width: 100% !important;
  overflow-x: hidden !important;
  position: relative !important;
  display: block !important; /* Ubah dari flex ke block seperti HTML asli */
}

/* Reset font family untuk konsistensi */
.sellzio-checkout-page * {
  font-family: '<PERSON><PERSON>', Arial, sans-serif !important;
  box-sizing: border-box !important;
}

/* Container layout dengan space untuk fixed header dan footer */
.sellzio-checkout-page .sellzio-checkout-container {
  max-width: 800px !important;
  margin: 0 auto !important;
  padding: 15px !important;
  padding-top: 80px !important; /* Space untuk fixed header */
  padding-bottom: 100px !important; /* Space untuk fixed footer */
  width: 100% !important;
}

/* Section styling sesuai HTML asli */
.sellzio-checkout-page .sellzio-checkout-section {
  background-color: #fff !important;
  border-radius: 8px !important;
  padding: 15px !important;
  margin-bottom: 8px !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

/* Force background colors */
.sellzio-checkout-page {
  background-color: #f5f5f5 !important;
}

.sellzio-checkout-page .sellzio-checkout-section,
.sellzio-checkout-page .sellzio-checkout-header,
.sellzio-checkout-page .sellzio-checkout-footer {
  background-color: #fff !important;
}

/* Force text colors */
.sellzio-checkout-page {
  color: #333 !important;
}

.sellzio-checkout-page .sellzio-address-phone,
.sellzio-checkout-page .sellzio-address-text,
.sellzio-checkout-page .sellzio-product-variant,
.sellzio-checkout-page .sellzio-quantity-display {
  color: #666 !important;
}

.sellzio-checkout-page .sellzio-edit-btn {
  color: #ee4d2d !important;
}

.sellzio-checkout-page .sellzio-product-price,
.sellzio-checkout-page .sellzio-order-total {
  color: #ee4d2d !important;
}

.sellzio-checkout-page .sellzio-price-original {
  color: #999 !important;
}

/* Force button styles */
.sellzio-checkout-page .sellzio-order-btn {
  background-color: #ee4d2d !important;
  color: white !important;
  border: none !important;
  padding: 12px 25px !important;
  border-radius: 4px !important;
  font-weight: bold !important;
  cursor: pointer !important;
}

.sellzio-checkout-page .sellzio-order-btn:hover {
  background-color: #d63c1e !important;
}

.sellzio-checkout-page .sellzio-order-btn:disabled {
  background-color: #aaa !important;
  cursor: not-allowed !important;
}

/* Header layout - Fixed floating */
.sellzio-checkout-page .sellzio-checkout-header {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  background-color: #fff !important;
  padding: 15px 0 !important;
  text-align: center !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
  z-index: 100 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Header container sesuai HTML asli */
.sellzio-checkout-page .sellzio-checkout-header-container {
  max-width: 800px !important;
  width: 100% !important;
  padding: 0 15px !important;
  margin: 0 auto !important;
  position: relative !important;
}

/* Footer layout - Fixed floating dengan container sejajar */
.sellzio-checkout-page .sellzio-checkout-footer {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  background-color: #fff !important;
  padding: 10px !important; /* Sesuai HTML asli: padding: 10px */
  box-shadow: 0 -2px 4px rgba(0,0,0,0.1) !important;
  display: flex !important;
  justify-content: center !important; /* Center untuk container */
  align-items: center !important;
  z-index: 50 !important;
}

/* Footer content container sejajar dengan checkout container */
.sellzio-checkout-page .sellzio-checkout-footer-inner {
  max-width: 800px !important;
  width: 100% !important;
  padding: 0 15px !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

/* Layout sesuai HTML asli */
.sellzio-checkout-page .sellzio-section-title {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-bottom: 10px !important;
  font-size: 1rem !important;
  font-weight: bold !important;
}

.sellzio-checkout-page .sellzio-product-item {
  display: flex !important;
  flex-direction: column !important;
  padding: 10px 0 !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.sellzio-checkout-page .sellzio-product-item:last-child {
  border-bottom: none !important;
}

.sellzio-checkout-page .sellzio-product-content {
  display: flex !important;
  margin-top: 5px !important;
}

.sellzio-checkout-page .sellzio-product-details {
  flex: 1 !important;
}

.sellzio-checkout-page .sellzio-product-actions {
  display: flex !important;
  justify-content: flex-start !important;
  align-items: center !important;
  margin-top: 10px !important;
}

.sellzio-checkout-page .sellzio-product-price {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  color: #ee4d2d !important;
  font-weight: bold !important;
}

/* Product layout sesuai HTML asli */
.sellzio-checkout-page .sellzio-product-item {
  display: flex !important;
  flex-direction: column !important;
  padding: 15px 0 !important;
  border-bottom: 1px solid #f0f0f0 !important;
}

.sellzio-checkout-page .sellzio-product-item:last-child {
  border-bottom: none !important;
}

.sellzio-checkout-page .sellzio-product-shop {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 10px !important;
  font-size: 14px !important;
  color: #333 !important;
  font-weight: 500 !important;
}

.sellzio-checkout-page .sellzio-shop-icon {
  margin-right: 8px !important;
  display: flex !important;
  align-items: center !important;
}

.sellzio-checkout-page .sellzio-product-content {
  display: flex !important;
  gap: 15px !important;
}

.sellzio-checkout-page .sellzio-product-image {
  width: 80px !important;
  height: 80px !important;
  border-radius: 4px !important;
  object-fit: cover !important;
  flex-shrink: 0 !important;
}

.sellzio-checkout-page .sellzio-product-details {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: space-between !important;
}

.sellzio-checkout-page .sellzio-product-name {
  font-weight: bold !important;
  font-size: 16px !important;
  margin-bottom: 5px !important;
  color: #333 !important;
}

.sellzio-checkout-page .sellzio-product-variant {
  font-size: 0.8rem !important;
  color: #666 !important;
  margin-bottom: 10px !important;
}

.sellzio-checkout-page .sellzio-product-actions {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
}

.sellzio-checkout-page .sellzio-product-price {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  color: #ee4d2d !important;
  font-weight: bold !important;
}

.sellzio-checkout-page .sellzio-price-original {
  color: #999 !important;
  text-decoration: line-through !important;
  font-weight: normal !important;
  font-size: 0.85em !important;
}

.sellzio-checkout-page .sellzio-quantity-display {
  font-size: 0.9rem !important;
  color: #666 !important;
}

.sellzio-checkout-page .sellzio-quantity {
  font-weight: bold !important;
  color: #333 !important;
}

/* Shipping section styling sesuai HTML asli */
.sellzio-checkout-page .sellzio-shipping-option-selected {
  position: relative !important;
  border: 1px solid #00BFA5 !important;
  border-radius: 8px !important;
  padding: 15px !important;
  margin-top: 10px !important;
  cursor: pointer !important;
  background-color: #fff !important;
}

.sellzio-checkout-page .sellzio-free-shipping-badge {
  position: absolute !important;
  top: -10px !important;
  left: 15px !important;
  background-color: #00BFA5 !important;
  color: white !important;
  padding: 2px 10px !important;
  border-radius: 12px !important;
  font-size: 12px !important;
  font-weight: bold !important;
  z-index: 1 !important;
}

.sellzio-checkout-page .sellzio-shipping-header {
  display: flex !important;
  justify-content: space-between !important;
  margin-bottom: 4px !important;
  align-items: center !important;
}

.sellzio-checkout-page .sellzio-shipping-name {
  font-weight: bold !important;
  font-size: 14px !important;
}

.sellzio-checkout-page .sellzio-shipping-price-container {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.sellzio-checkout-page .sellzio-shipping-original-price {
  color: #999 !important;
  text-decoration: line-through !important;
  font-size: 14px !important;
  margin-right: 8px !important;
}

.sellzio-checkout-page .sellzio-shipping-price {
  font-weight: bold !important;
  font-size: 16px !important;
}

.sellzio-checkout-page .sellzio-shipping-check {
  color: #00BFA5 !important;
  margin-left: 5px !important;
}

.sellzio-checkout-page .sellzio-shipping-estimate {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 4px !important;
  color: #00BFA5 !important;
}

.sellzio-checkout-page .sellzio-truck-icon {
  margin-right: 8px !important;
  display: flex !important;
  align-items: center !important;
}

.sellzio-checkout-page .sellzio-estimate-text {
  font-weight: 500 !important;
  color: #00BFA5 !important;
  font-size: 12px !important;
}

.sellzio-checkout-page .sellzio-shipping-guarantee {
  color: #666 !important;
  font-size: 12px !important;
  margin-bottom: -10px !important;
}

/* Payment section styling sesuai HTML asli */
.sellzio-checkout-page .sellzio-payment-section {
  cursor: pointer !important;
}

.sellzio-checkout-page .sellzio-payment-content {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.sellzio-checkout-page .sellzio-payment-left {
  display: flex !important;
  align-items: center !important;
}

.sellzio-checkout-page .sellzio-payment-icon {
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-right: 8px !important;
}

.sellzio-checkout-page .sellzio-payment-label {
  font-weight: 500 !important;
  font-size: 14px !important;
  white-space: nowrap !important;
}

.sellzio-checkout-page .sellzio-payment-right {
  display: flex !important;
  align-items: center !important;
}

.sellzio-checkout-page .sellzio-payment-method {
  margin-right: 10px !important;
  color: #666 !important;
  font-size: 14px !important;
}

.sellzio-checkout-page .sellzio-payment-arrow {
  color: #999 !important;
  font-size: 30px !important;
}

/* Voucher section styling sesuai HTML asli */
.sellzio-checkout-page .sellzio-voucher-option {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 12px 0 !important;
  cursor: pointer !important;
}

.sellzio-checkout-page .sellzio-voucher-label {
  display: flex !important;
  align-items: center !important;
}

.sellzio-checkout-page .sellzio-voucher-icon {
  margin-right: 10px !important;
  font-size: 1.2rem !important;
}

.sellzio-checkout-page .sellzio-voucher-applied {
  display: flex !important;
  align-items: center !important;
  gap: 10px !important;
}

.sellzio-checkout-page .sellzio-voucher-count {
  color: #00BFA5 !important;
  font-size: 14px !important;
}

/* Payment summary styling sesuai HTML asli */
.sellzio-checkout-page .sellzio-cost-breakdown {
  margin-top: 10px !important;
}

.sellzio-checkout-page .sellzio-cost-item {
  display: flex !important;
  justify-content: space-between !important;
  margin-bottom: 8px !important;
}

.sellzio-checkout-page .sellzio-discount {
  color: #00BFA5 !important;
}

.sellzio-checkout-page .sellzio-total-cost {
  display: flex !important;
  justify-content: space-between !important;
  font-weight: bold !important;
  font-size: 1.1rem !important;
  margin-top: 10px !important;
  padding-top: 10px !important;
  border-top: 1px solid #f0f0f0 !important;
}

.sellzio-checkout-page .sellzio-total-price {
  color: #ee4d2d !important;
}

/* Force font weights */
.sellzio-checkout-page .sellzio-checkout-title,
.sellzio-checkout-page .sellzio-section-title,
.sellzio-checkout-page .sellzio-address-name,
.sellzio-checkout-page .sellzio-product-name,
.sellzio-checkout-page .sellzio-quantity,
.sellzio-checkout-page .sellzio-product-price,
.sellzio-checkout-page .sellzio-order-total,
.sellzio-checkout-page .sellzio-order-btn {
  font-weight: bold !important;
}

/* Force font sizes */
.sellzio-checkout-page .sellzio-checkout-title {
  font-size: 1.2rem !important;
}

.sellzio-checkout-page .sellzio-section-title {
  font-size: 1rem !important;
}

.sellzio-checkout-page .sellzio-address-info {
  font-size: 14px !important;
}

.sellzio-checkout-page .sellzio-product-name {
  font-size: 16px !important;
}

.sellzio-checkout-page .sellzio-product-variant {
  font-size: 0.8rem !important;
}

.sellzio-checkout-page .sellzio-quantity-display {
  font-size: 0.9rem !important;
}

.sellzio-checkout-page .sellzio-order-total {
  font-size: 1.1rem !important;
}

.sellzio-checkout-page .sellzio-edit-btn {
  font-size: 0.9rem !important;
  font-weight: normal !important;
}

/* Force borders dan shadows */
.sellzio-checkout-page .sellzio-checkout-section {
  border-radius: 8px !important;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
}

.sellzio-checkout-page .sellzio-product-image {
  width: 80px !important;
  height: 80px !important;
  border-radius: 4px !important;
  object-fit: cover !important;
  margin-right: 15px !important;
}

/* Force cursor styles */
.sellzio-checkout-page .sellzio-edit-btn,
.sellzio-checkout-page .sellzio-order-btn,
.sellzio-checkout-page .sellzio-checkout-back-btn {
  cursor: pointer !important;
}

/* Force positioning */
.sellzio-checkout-page .sellzio-checkout-back-btn {
  position: absolute !important;
  left: 15px !important;
}

.sellzio-checkout-page .sellzio-quantity-display {
  margin-left: auto !important;
}

/* Force text decorations */
.sellzio-checkout-page .sellzio-price-original {
  text-decoration: line-through !important;
}

/* Modal styling dengan z-index tinggi */
.sellzio-checkout-page .sellzio-modal-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  z-index: 1000 !important; /* Lebih tinggi dari header (100) dan footer (50) */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 20px !important;
}

.sellzio-checkout-page .sellzio-modal {
  background-color: #fff !important;
  border-radius: 8px !important;
  max-width: 500px !important;
  width: 100% !important;
  max-height: 90vh !important;
  overflow-y: auto !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
  position: relative !important;
  z-index: 1001 !important;
}

.sellzio-checkout-page .sellzio-modal-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 20px !important;
  border-bottom: 1px solid #e0e0e0 !important;
}

.sellzio-checkout-page .sellzio-modal-header h2 {
  margin: 0 !important;
  font-size: 1.2rem !important;
  font-weight: bold !important;
  color: #333 !important;
}

.sellzio-checkout-page .sellzio-modal-close {
  background: none !important;
  border: none !important;
  cursor: pointer !important;
  padding: 5px !important;
  color: #666 !important;
}

.sellzio-checkout-page .sellzio-modal-content {
  padding: 20px !important;
}

.sellzio-checkout-page .sellzio-modal-footer {
  display: flex !important;
  justify-content: flex-end !important;
  gap: 10px !important;
  padding: 20px !important;
  border-top: 1px solid #e0e0e0 !important;
}

/* Form styling dalam modal */
.sellzio-checkout-page .sellzio-form-group {
  margin-bottom: 20px !important;
}

.sellzio-checkout-page .sellzio-form-label {
  display: block !important;
  margin-bottom: 8px !important;
  font-weight: 500 !important;
  color: #333 !important;
}

.sellzio-checkout-page .sellzio-form-input,
.sellzio-checkout-page .sellzio-form-textarea {
  width: 100% !important;
  padding: 12px !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  font-size: 14px !important;
  transition: border-color 0.3s !important;
  font-family: 'Roboto', Arial, sans-serif !important;
}

.sellzio-checkout-page .sellzio-form-input:focus,
.sellzio-checkout-page .sellzio-form-textarea:focus {
  outline: none !important;
  border-color: #ee4d2d !important;
}

.sellzio-checkout-page .sellzio-form-input.error,
.sellzio-checkout-page .sellzio-form-textarea.error {
  border-color: #ef4444 !important;
}

.sellzio-checkout-page .sellzio-form-error {
  display: block !important;
  color: #ef4444 !important;
  font-size: 12px !important;
  margin-top: 4px !important;
}

.sellzio-checkout-page .sellzio-form-textarea {
  resize: vertical !important;
  min-height: 80px !important;
}

/* Button styling dalam modal */
.sellzio-checkout-page .sellzio-btn-primary {
  background-color: #ee4d2d !important;
  color: white !important;
  border: none !important;
  padding: 12px 24px !important;
  border-radius: 4px !important;
  font-weight: bold !important;
  cursor: pointer !important;
  transition: background-color 0.3s !important;
  font-family: 'Roboto', Arial, sans-serif !important;
}

.sellzio-checkout-page .sellzio-btn-primary:hover {
  background-color: #d63c1e !important;
}

.sellzio-checkout-page .sellzio-btn-secondary {
  background-color: #fff !important;
  color: #666 !important;
  border: 1px solid #ddd !important;
  padding: 12px 24px !important;
  border-radius: 4px !important;
  font-weight: bold !important;
  cursor: pointer !important;
  transition: background-color 0.3s !important;
  font-family: 'Roboto', Arial, sans-serif !important;
}

.sellzio-checkout-page .sellzio-btn-secondary:hover {
  background-color: #f5f5f5 !important;
}

/* Voucher Modal Styling */
.sellzio-checkout-page .sellzio-voucher-modal {
  background-color: #fff !important;
  border-radius: 8px !important;
  max-width: 600px !important;
  width: 100% !important;
  max-height: 90vh !important;
  overflow: hidden !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
  position: relative !important;
  z-index: 1001 !important;
  display: flex !important;
  flex-direction: column !important;
}

.sellzio-checkout-page .sellzio-search-bar {
  margin-bottom: 20px !important;
}

.sellzio-checkout-page .sellzio-search-input {
  position: relative !important;
  display: flex !important;
  align-items: center !important;
  border: 1px solid #ddd !important;
  border-radius: 8px !important;
  padding: 12px !important;
  background-color: #f8f9fa !important;
}

.sellzio-checkout-page .sellzio-search-input input {
  flex: 1 !important;
  border: none !important;
  outline: none !important;
  background: transparent !important;
  margin-left: 8px !important;
  font-size: 14px !important;
  font-family: 'Roboto', Arial, sans-serif !important;
}

.sellzio-checkout-page .sellzio-search-icon {
  color: #666 !important;
}

.sellzio-checkout-page .sellzio-clear-icon {
  color: #999 !important;
  cursor: pointer !important;
  margin-left: 8px !important;
}

.sellzio-checkout-page .sellzio-voucher-tabs {
  display: flex !important;
  gap: 8px !important;
  margin-bottom: 20px !important;
  overflow-x: auto !important;
}

.sellzio-checkout-page .sellzio-voucher-tab {
  padding: 8px 16px !important;
  border: 1px solid #ddd !important;
  border-radius: 20px !important;
  background-color: #fff !important;
  color: #666 !important;
  font-size: 14px !important;
  cursor: pointer !important;
  white-space: nowrap !important;
  transition: all 0.3s !important;
  font-family: 'Roboto', Arial, sans-serif !important;
}

.sellzio-checkout-page .sellzio-voucher-tab.active {
  background-color: #ee4d2d !important;
  color: white !important;
  border-color: #ee4d2d !important;
}

.sellzio-checkout-page .sellzio-voucher-list {
  flex: 1 !important;
  overflow-y: auto !important;
  max-height: 400px !important;
}

.sellzio-checkout-page .sellzio-voucher-item {
  display: flex !important;
  padding: 16px !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 8px !important;
  margin-bottom: 12px !important;
  position: relative !important;
  background-color: #fff !important;
  transition: all 0.3s !important;
}

.sellzio-checkout-page .sellzio-voucher-item:hover {
  border-color: #ee4d2d !important;
  box-shadow: 0 2px 8px rgba(238, 77, 45, 0.1) !important;
}

.sellzio-checkout-page .sellzio-recommended-badge {
  position: absolute !important;
  top: -8px !important;
  left: 16px !important;
  background-color: #ff6b35 !important;
  color: white !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  font-size: 10px !important;
  font-weight: bold !important;
  z-index: 1 !important;
}

.sellzio-checkout-page .sellzio-voucher-left {
  width: 80px !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  border-right: 1px dashed #ddd !important;
  margin-right: 16px !important;
  padding-right: 16px !important;
}

.sellzio-checkout-page .sellzio-voucher-amount {
  font-size: 18px !important;
  font-weight: bold !important;
  color: #ee4d2d !important;
  text-align: center !important;
  margin-bottom: 4px !important;
}

.sellzio-checkout-page .sellzio-voucher-min {
  font-size: 10px !important;
  color: #666 !important;
  text-align: center !important;
}

.sellzio-checkout-page .sellzio-voucher-right {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
}

.sellzio-checkout-page .sellzio-voucher-title {
  font-size: 16px !important;
  font-weight: bold !important;
  color: #333 !important;
  margin-bottom: 4px !important;
}

.sellzio-checkout-page .sellzio-voucher-desc {
  font-size: 14px !important;
  color: #666 !important;
  margin-bottom: 8px !important;
}

.sellzio-checkout-page .sellzio-voucher-info {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  margin-top: auto !important;
}

.sellzio-checkout-page .sellzio-voucher-date {
  font-size: 12px !important;
  color: #999 !important;
}

/* Circular Checkbox Styling */
.sellzio-checkout-page .sellzio-circular-checkbox {
  position: relative !important;
  display: inline-block !important;
  width: 20px !important;
  height: 20px !important;
}

.sellzio-checkout-page .sellzio-circular-checkbox input[type="checkbox"] {
  opacity: 0 !important;
  position: absolute !important;
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  cursor: pointer !important;
}

.sellzio-checkout-page .sellzio-checkmark {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 20px !important;
  height: 20px !important;
  border: 2px solid #ddd !important;
  border-radius: 50% !important;
  background-color: #fff !important;
  transition: all 0.3s !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.sellzio-checkout-page .sellzio-circular-checkbox input[type="checkbox"]:checked + .sellzio-checkmark {
  background-color: #ee4d2d !important;
  border-color: #ee4d2d !important;
}

.sellzio-checkout-page .sellzio-circular-checkbox input[type="checkbox"]:checked + .sellzio-checkmark::after {
  content: '✓' !important;
  color: white !important;
  font-size: 12px !important;
  font-weight: bold !important;
}

.sellzio-checkout-page .sellzio-no-results {
  text-align: center !important;
  padding: 40px 20px !important;
  color: #666 !important;
  font-size: 14px !important;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .sellzio-checkout-page .sellzio-checkout-container {
    padding: 10px !important;
    padding-top: 80px !important;
    padding-bottom: 100px !important;
  }

  .sellzio-checkout-page .sellzio-checkout-section {
    padding: 12px !important;
  }

  .sellzio-checkout-page .sellzio-product-image {
    width: 60px !important;
    height: 60px !important;
  }

  .sellzio-checkout-page .sellzio-order-btn {
    padding: 10px 20px !important;
  }

  .sellzio-checkout-page .sellzio-modal {
    margin: 10px !important;
    max-height: 95vh !important;
  }

  .sellzio-checkout-page .sellzio-modal-header,
  .sellzio-checkout-page .sellzio-modal-content,
  .sellzio-checkout-page .sellzio-modal-footer {
    padding: 15px !important;
  }

  .sellzio-checkout-page .sellzio-modal-footer {
    flex-direction: column !important;
  }

  .sellzio-checkout-page .sellzio-btn-primary,
  .sellzio-checkout-page .sellzio-btn-secondary {
    width: 100% !important;
    margin-bottom: 10px !important;
  }

  .sellzio-checkout-page .sellzio-btn-primary {
    order: 1 !important;
  }

  .sellzio-checkout-page .sellzio-btn-secondary {
    order: 2 !important;
    margin-bottom: 0 !important;
  }
}
