"use client"

import React from 'react'

interface SellzioCheckoutFooterProps {
  total: number
  onPlaceOrder: () => void
  isProcessing?: boolean
}

export const SellzioCheckoutFooter: React.FC<SellzioCheckoutFooterProps> = ({
  total,
  onPlaceOrder,
  isProcessing = false
}) => {
  const formatPrice = (price: number) => {
    return `Rp${price.toLocaleString('id-ID')}`
  }

  return (
    <footer className="sellzio-checkout-footer">
      <div className="sellzio-order-total">
        {formatPrice(total)}
      </div>
      <button
        className="sellzio-order-btn"
        onClick={onPlaceOrder}
        disabled={isProcessing}
      >
        {isProcessing ? 'Memproses...' : 'Buat Pesanan'}
      </button>
    </footer>
  )
}
