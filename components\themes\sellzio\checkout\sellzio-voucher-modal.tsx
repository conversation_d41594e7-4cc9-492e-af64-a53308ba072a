"use client"

import React, { useState } from 'react'
import { ArrowLeft, Search, X } from 'lucide-react'

interface Voucher {
  id: string
  type: string
  title: string
  description: string
  amount: string
  minPurchase: string
  validUntil: string
  isRecommended?: boolean
  isDisabled?: boolean
}

interface SellzioVoucherModalProps {
  isOpen: boolean
  onClose: () => void
  onApplyVouchers: (vouchers: any[]) => void
  appliedVouchers: any[]
}

export const SellzioVoucherModal: React.FC<SellzioVoucherModalProps> = ({
  isOpen,
  onClose,
  onApplyVouchers,
  appliedVouchers
}) => {
  const [activeTab, setActiveTab] = useState('semua')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedVouchers, setSelectedVouchers] = useState<string[]>([])
  const [showSearchSuggestions, setShowSearchSuggestions] = useState(false)

  // Sample voucher data
  const sampleVouchers: Voucher[] = [
    {
      id: 'v1',
      type: 'discount',
      title: 'Diskon 50%',
      description: 'Berlaku untuk semua produk fashion',
      amount: '50%',
      minPurchase: 'Min. Rp100.000',
      validUntil: 'Berlaku hingga 31 Des',
      isRecommended: true
    },
    {
      id: 'v2',
      type: 'shipping',
      title: 'Gratis Ongkir',
      description: 'Gratis ongkos kirim ke seluruh Indonesia',
      amount: 'GRATIS',
      minPurchase: 'Min. Rp50.000',
      validUntil: 'Berlaku hingga 25 Des'
    },
    {
      id: 'v3',
      type: 'discount',
      title: 'Diskon Rp25.000',
      description: 'Potongan langsung untuk pembelian pertama',
      amount: 'Rp25K',
      minPurchase: 'Min. Rp150.000',
      validUntil: 'Berlaku hingga 30 Des'
    },
    {
      id: 'v4',
      type: 'cashback',
      title: 'Cashback 20%',
      description: 'Cashback maksimal Rp50.000',
      amount: '20%',
      minPurchase: 'Min. Rp200.000',
      validUntil: 'Berlaku hingga 28 Des'
    }
  ]

  const tabs = [
    { id: 'semua', label: 'Semua' },
    { id: 'discount', label: 'Diskon' },
    { id: 'shipping', label: 'Gratis Ongkir' },
    { id: 'cashback', label: 'Cashback' },
    { id: 'payment', label: 'Pembayaran' }
  ]

  const filteredVouchers = sampleVouchers.filter(voucher => {
    const matchesTab = activeTab === 'semua' || voucher.type === activeTab
    const matchesSearch = voucher.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         voucher.description.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesTab && matchesSearch
  })

  const handleVoucherSelect = (voucherId: string) => {
    setSelectedVouchers(prev => {
      if (prev.includes(voucherId)) {
        return prev.filter(id => id !== voucherId)
      } else {
        return [...prev, voucherId]
      }
    })
  }

  const handleApplyVouchers = () => {
    const selectedVoucherData = sampleVouchers
      .filter(v => selectedVouchers.includes(v.id))
      .map(v => ({
        id: v.id,
        name: v.title,
        type: v.type === 'discount' ? 'voucher-badge-discount' : 'voucher-badge-shipping',
        amount: v.amount
      }))
    
    onApplyVouchers(selectedVoucherData)
    onClose()
  }

  const getVoucherTypeClass = (type: string) => {
    switch (type) {
      case 'shipping': return 'sellzio-voucher-shipping'
      case 'cashback': return 'sellzio-voucher-cashback'
      case 'payment': return 'sellzio-voucher-payment'
      case 'bank': return 'sellzio-voucher-bank'
      default: return 'sellzio-voucher-discount'
    }
  }

  if (!isOpen) return null

  return (
    <div className="sellzio-modal-overlay">
      <div className="sellzio-voucher-modal">
        {/* Header */}
        <div className="sellzio-modal-header">
          <button className="sellzio-modal-close" onClick={onClose}>
            <ArrowLeft size={20} />
          </button>
          <h2>Pilih Voucher</h2>
        </div>

        {/* Content */}
        <div className="sellzio-modal-content">
          {/* Search Bar */}
          <div className="sellzio-search-bar">
            <div className="sellzio-search-input">
              <Search className="sellzio-search-icon" size={18} />
              <input
                type="text"
                placeholder="Cari voucher..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              {searchQuery && (
                <X 
                  className="sellzio-clear-icon" 
                  size={18}
                  onClick={() => setSearchQuery('')}
                />
              )}
            </div>
          </div>

          {/* Tabs */}
          <div className="sellzio-voucher-tabs">
            {tabs.map(tab => (
              <button
                key={tab.id}
                className={`sellzio-voucher-tab ${activeTab === tab.id ? 'active' : ''}`}
                onClick={() => setActiveTab(tab.id)}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Voucher List */}
          <div className="sellzio-voucher-list">
            {filteredVouchers.map(voucher => (
              <div 
                key={voucher.id}
                className={`sellzio-voucher-item ${getVoucherTypeClass(voucher.type)} ${voucher.isDisabled ? 'disabled' : ''}`}
              >
                {voucher.isRecommended && (
                  <div className="sellzio-recommended-badge">Direkomendasikan</div>
                )}

                <div className="sellzio-voucher-left">
                  <div className="sellzio-voucher-amount">{voucher.amount}</div>
                  <div className="sellzio-voucher-min">{voucher.minPurchase}</div>
                </div>

                <div className="sellzio-voucher-right">
                  <div className="sellzio-voucher-title">{voucher.title}</div>
                  <div className="sellzio-voucher-desc">{voucher.description}</div>
                  <div className="sellzio-voucher-info">
                    <div className="sellzio-voucher-date">{voucher.validUntil}</div>
                    <div className="sellzio-circular-checkbox">
                      <input
                        type="checkbox"
                        checked={selectedVouchers.includes(voucher.id)}
                        onChange={() => handleVoucherSelect(voucher.id)}
                        disabled={voucher.isDisabled}
                      />
                      <span className="sellzio-checkmark"></span>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {filteredVouchers.length === 0 && (
              <div className="sellzio-no-results">
                Tidak ada voucher yang sesuai dengan pencarian Anda
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="sellzio-modal-footer">
          <button 
            className="sellzio-btn-primary"
            onClick={handleApplyVouchers}
            disabled={selectedVouchers.length === 0}
          >
            Terapkan ({selectedVouchers.length})
          </button>
        </div>
      </div>
    </div>
  )
}
